import { AuthFormLayout } from '../layouts';
import { BodyLoginForm, type loginSchemaProps } from '../molecules/body-login-form';
import { FooterLoginForm } from '../molecules/footer-login-form';
import { HeaderFormLogin } from '../molecules/header-login-form';


export const LoginForm = () => {


  const onSubmit = async (data: loginSchemaProps) => {
    try {
      console.log(data);
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <AuthFormLayout
      imgUrl="https://img.freepik.com/fotos-gratis/equipa-a-trabalhar-em-conjunto-num-projecto_23-2149273739.jpg?semt=ais_hybrid&w=740&q=80"
      imagePosition="right"
    >
      <HeaderFormLogin />
      <BodyLoginForm onSubmit={onSubmit} />
      <FooterLoginForm />
    </AuthFormLayout>
  );
};
