name: Build and Deploy to GCS

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  BUCKET_NAME: ${{ secrets.GCS_BUCKET_NAME }}
  SERVICE_ACCOUNT_KEY: ${{ secrets.GCP_SA_KEY }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linter
      run: npm run lint

    - name: Build project
      run: npm run build
      env:
        CI: false

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        service_account_key: ${{ env.SERVICE_ACCOUNT_KEY }}
        export_default_credentials: true

    - name: Configure gsutil
      run: |
        echo '${{ env.SERVICE_ACCOUNT_KEY }}' | base64 -d > ${HOME}/gcloud-service-key.json
        gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
        gcloud config set project ${{ env.PROJECT_ID }}

    - name: Deploy to Google Cloud Storage
      run: |
        # Remove arquivos antigos do bucket (opcional)
        gsutil -m rm -r gs://${{ env.BUCKET_NAME }}/* || true
        
        # Upload dos arquivos buildados
        gsutil -m cp -r dist/* gs://${{ env.BUCKET_NAME }}/
        
        # Configurar cache headers para assets estáticos
        gsutil -m setmeta -h "Cache-Control:public, max-age=********" gs://${{ env.BUCKET_NAME }}/**/*.{js,css,png,jpg,jpeg,gif,ico,svg,woff,woff2,ttf,eot}
        
        # Configurar cache headers para HTML (sem cache)
        gsutil -m setmeta -h "Cache-Control:no-cache, no-store, must-revalidate" gs://${{ env.BUCKET_NAME }}/**/*.html
        
        # Configurar index.html como página principal
        gsutil web set -m index.html -e 404.html gs://${{ env.BUCKET_NAME }}

    - name: Make bucket publicly readable
      run: |
        gsutil iam ch allUsers:objectViewer gs://${{ env.BUCKET_NAME }}

    - name: Get website URL
      run: |
        echo "🚀 Website deployed successfully!"
        echo "📍 URL: https://storage.googleapis.com/${{ env.BUCKET_NAME }}/index.html"
        echo "🌐 Custom domain URL (if configured): https://${{ env.BUCKET_NAME }}"

    - name: Cleanup
      if: always()
      run: |
        rm -f ${HOME}/gcloud-service-key.json
