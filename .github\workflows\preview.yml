name: Preview Build

on:
  pull_request:
    branches: [ main ]

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  PREVIEW_BUCKET_NAME: ${{ secrets.GCS_PREVIEW_BUCKET_NAME }}
  SERVICE_ACCOUNT_KEY: ${{ secrets.GCP_SA_KEY }}

jobs:
  preview-build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linter
      run: npm run lint

    - name: Build project
      run: npm run build
      env:
        CI: false

    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}
        service_account_key: ${{ env.SERVICE_ACCOUNT_KEY }}
        export_default_credentials: true

    - name: Deploy preview to G<PERSON>
      run: |
        # Criar pasta única para o PR
        PR_FOLDER="pr-${{ github.event.number }}"
        
        # Upload dos arquivos buildados para pasta do PR
        gsutil -m cp -r dist/* gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/
        
        # Configurar cache headers
        gsutil -m setmeta -h "Cache-Control:public, max-age=3600" gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/**/*.{js,css,png,jpg,jpeg,gif,ico,svg,woff,woff2,ttf,eot}
        gsutil -m setmeta -h "Cache-Control:no-cache" gs://${{ env.PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/**/*.html

    - name: Comment PR with preview URL
      uses: actions/github-script@v7
      with:
        script: |
          const prNumber = context.issue.number;
          const previewUrl = `https://storage.googleapis.com/${{ env.PREVIEW_BUCKET_NAME }}/pr-${prNumber}/index.html`;
          
          github.rest.issues.createComment({
            issue_number: prNumber,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `🚀 **Preview Deploy Ready!**
            
            📍 **Preview URL:** ${previewUrl}
            
            This preview will be available until the PR is merged or closed.`
          });

  cleanup-preview:
    runs-on: ubuntu-latest
    if: github.event.action == 'closed'
    
    steps:
    - name: Setup Google Cloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true

    - name: Cleanup preview files
      run: |
        PR_FOLDER="pr-${{ github.event.number }}"
        gsutil -m rm -r gs://${{ secrets.GCS_PREVIEW_BUCKET_NAME }}/${PR_FOLDER}/ || true
